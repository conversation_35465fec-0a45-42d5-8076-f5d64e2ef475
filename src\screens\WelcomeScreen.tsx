import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from '../theme/ThemeContext';
import {useNavigation, ScreenNames} from '../../App';
import Colors from '../theme/colors';
import LinearGradient from 'react-native-linear-gradient';

const {width} = Dimensions.get('window');

const WelcomeScreen = (): React.JSX.Element => {
  const {isDarkMode, isNeonMode} = useTheme();
  const {navigateTo} = useNavigation();
  const insets = useSafeAreaInsets();

  // Get colors based on the active theme
  let backgroundColor: string,
    textColor: string,
    textSecondaryColor: string,
    primaryColor: string,
    cardBgColor: string;

  if (isNeonMode) {
    backgroundColor = Colors.neon.background;
    textColor = Colors.neon.text;
    textSecondaryColor = Colors.neon.textSecondary;
    primaryColor = Colors.neon.primary;
    cardBgColor = Colors.neon.cardBg;
  } else {
    backgroundColor = isDarkMode
      ? Colors.dark.background
      : Colors.light.background;
    textColor = isDarkMode ? Colors.dark.text : Colors.light.text;
    textSecondaryColor = isDarkMode
      ? Colors.dark.textSecondary
      : Colors.light.textSecondary;
    primaryColor = isDarkMode ? Colors.dark.primary : Colors.light.primary;
    cardBgColor = isDarkMode ? Colors.dark.cardBg : Colors.light.cardBg;
  }

  const handleGetStarted = () => {
    navigateTo(ScreenNames.MAIN);
  };

  const FeatureCard = ({
    icon,
    title,
    description,
  }: {
    icon: string;
    title: string;
    description: string;
  }) => (
    <View
      style={[
        styles.featureCard,
        {
          backgroundColor: cardBgColor,
          borderColor: isNeonMode ? Colors.neon.border : Colors.light.border,
        },
        isNeonMode && {
          shadowColor: Colors.neon.glow.cyan,
          shadowOffset: {width: 0, height: 0},
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 8,
        },
      ]}>
      <Text
        style={[
          styles.featureIcon,
          isNeonMode && {
            color: Colors.neon.secondary,
            textShadowColor: Colors.neon.secondary,
            textShadowOffset: {width: 0, height: 0},
            textShadowRadius: 5,
          },
        ]}>
        {icon}
      </Text>
      <Text
        style={[
          styles.featureTitle,
          {color: textColor},
          isNeonMode && {
            color: Colors.neon.text,
            textShadowColor: Colors.neon.glow.magenta,
            textShadowOffset: {width: 0, height: 0},
            textShadowRadius: 3,
          },
        ]}>
        {title}
      </Text>
      <Text style={[styles.featureDescription, {color: textSecondaryColor}]}>
        {description}
      </Text>
    </View>
  );

  const renderGetStartedButton = () => {
    if (isNeonMode) {
      return (
        <LinearGradient
          colors={[Colors.neon.glow.magenta, Colors.neon.glow.cyan]}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 0}}
          style={styles.gradientButton}>
          <TouchableOpacity
            style={styles.getStartedButtonInner}
            onPress={handleGetStarted}
            activeOpacity={0.8}>
            <Text
              style={[
                styles.getStartedButtonText,
                {color: Colors.neon.background},
              ]}>
              Get Started
            </Text>
          </TouchableOpacity>
        </LinearGradient>
      );
    }

    return (
      <TouchableOpacity
        style={[styles.getStartedButton, {backgroundColor: primaryColor}]}
        onPress={handleGetStarted}
        activeOpacity={0.8}>
        <Text style={[styles.getStartedButtonText, {color: '#FFFFFF'}]}>
          Get Started
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor}]}
      edges={['left', 'right', 'top']}>
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={[
          styles.scrollContent,
          {paddingBottom: Math.max(20, insets.bottom)},
        ]}
        showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <View style={styles.headerSection}>
          <View style={styles.iconContainer}>
            <Text
              style={[
                styles.appIcon,
                {color: primaryColor},
                isNeonMode && {
                  color: Colors.neon.primary,
                  textShadowColor: Colors.neon.primary,
                  textShadowOffset: {width: 0, height: 0},
                  textShadowRadius: 10,
                },
              ]}>
              🤱
            </Text>
          </View>

          <Text
            style={[
              styles.appTitle,
              {color: textColor},
              isNeonMode && {
                color: Colors.neon.text,
                textShadowColor: Colors.neon.glow.magenta,
                textShadowOffset: {width: 0, height: 0},
                textShadowRadius: 5,
              },
            ]}>
            Pregnancy Tips
          </Text>

          <Text style={[styles.appSubtitle, {color: textSecondaryColor}]}>
            Your daily companion for a healthy pregnancy journey
          </Text>
        </View>

        {/* Description Section */}
        <View style={styles.descriptionSection}>
          <Text style={[styles.descriptionText, {color: textSecondaryColor}]}>
            Get expert-backed tips and guidance throughout your pregnancy. From
            nutrition and exercise to prenatal care and wellness, we're here to
            support you every step of the way.
          </Text>
        </View>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <Text
            style={[
              styles.sectionTitle,
              {color: textColor},
              isNeonMode && {
                color: Colors.neon.secondary,
                textShadowColor: Colors.neon.secondary,
                textShadowOffset: {width: 0, height: 0},
                textShadowRadius: 3,
              },
            ]}>
            What You'll Get
          </Text>

          <FeatureCard
            icon="📚"
            title="Daily Tips"
            description="Receive carefully curated daily tips covering all aspects of pregnancy health and wellness."
          />

          <FeatureCard
            icon="🍎"
            title="Nutrition Guidance"
            description="Learn about proper nutrition, essential vitamins, and foods to avoid during pregnancy."
          />

          <FeatureCard
            icon="🏃‍♀️"
            title="Exercise & Wellness"
            description="Discover safe exercises and wellness practices to keep you and your baby healthy."
          />

          <FeatureCard
            icon="👩‍⚕️"
            title="Medical Insights"
            description="Get expert advice on prenatal care, check-ups, and important health considerations."
          />
        </View>

        {/* Get Started Button */}
        <View style={styles.buttonSection}>{renderGetStartedButton()}</View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  headerSection: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 32,
  },
  iconContainer: {
    marginBottom: 16,
  },
  appIcon: {
    fontSize: 80,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  appSubtitle: {
    fontSize: 18,
    textAlign: 'center',
    lineHeight: 24,
  },
  descriptionSection: {
    marginBottom: 40,
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
  },
  featuresSection: {
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
  },
  featureCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  featureIcon: {
    fontSize: 32,
    textAlign: 'center',
    marginBottom: 12,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  buttonSection: {
    paddingTop: 20,
    alignItems: 'center',
  },
  getStartedButton: {
    paddingVertical: 16,
    paddingHorizontal: 48,
    borderRadius: 25,
    minWidth: width * 0.6,
    alignItems: 'center',
  },
  gradientButton: {
    borderRadius: 25,
    minWidth: width * 0.6,
  },
  getStartedButtonInner: {
    paddingVertical: 16,
    paddingHorizontal: 48,
    alignItems: 'center',
    width: '100%',
  },
  getStartedButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
});

export default WelcomeScreen;
