import React from 'react';
import {Animated} from 'react-native';
import {TipWithDetails} from '../../models/types';
import TipListItem from './TipListItem';

interface AnimatedTipListItemProps {
  item: TipWithDetails;
  index: number;
  fadeAnim: Animated.Value;
  onPress: (tipId: number) => void;
}

const AnimatedTipListItem: React.FC<AnimatedTipListItemProps> = ({
  item,
  index,
  fadeAnim,
  onPress,
}) => {
  return (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [
          {
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [50 + index * 10, 0],
            }),
          },
        ],
      }}>
      <TipListItem item={item} onPress={onPress} />
    </Animated.View>
  );
};

export default AnimatedTipListItem;
