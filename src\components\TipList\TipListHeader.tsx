import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useTheme} from '../../theme/ThemeContext';
import Colors from '../../theme/colors';

interface TipListHeaderProps {
  onBackPress: () => void;
  borderColor: string;
}

const TipListHeader: React.FC<TipListHeaderProps> = ({
  onBackPress,
  borderColor,
}) => {
  const {isDarkMode, isNeonMode} = useTheme();

  // Get colors based on the active theme
  let textColor, primaryColor;

  if (isNeonMode) {
    textColor = Colors.neon.text;
    primaryColor = Colors.neon.primary;
  } else {
    textColor = isDarkMode ? Colors.dark.text : Colors.light.text;
    primaryColor = isDarkMode ? Colors.dark.primary : Colors.light.primary;
  }

  return (
    <View style={[styles.header, {borderBottomColor: borderColor}]}>
      <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
        <Text
          style={[
            styles.backButtonText,
            {
              color: isNeonMode ? Colors.neon.glow.cyan : textColor,
              ...(isNeonMode && {
                textShadowColor: Colors.neon.glow.blue,
                textShadowOffset: {width: 0, height: 0},
                textShadowRadius: 5,
              }),
            },
          ]}>
          ← Back
        </Text>
      </TouchableOpacity>
      <Text
        style={[
          styles.headerTitle,
          {color: isNeonMode ? Colors.neon.glow.magenta : textColor},
          isNeonMode && {
            textShadowColor: primaryColor,
            textShadowRadius: 5,
            textShadowOffset: {width: 0, height: 0},
          },
        ]}>
        All Tips
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
    marginRight: 16, // To balance the back button
    letterSpacing: 0.5,
  },
});

export default TipListHeader;
