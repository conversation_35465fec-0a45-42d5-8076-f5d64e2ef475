import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Animated,
  Easing,
} from 'react-native';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from '../theme/ThemeContext';
import Colors from '../theme/colors';
import {useNavigation} from '../../App';
import {ScreenNames} from '../../App';
import {getAllTips} from '../database/tips/getAllTips';
import {TipWithDetails} from '../models/types';
import {TipListHeader, AnimatedTipListItem} from '../components/TipList';
import LinearGradient from 'react-native-linear-gradient';

const AllTipsScreen = () => {
  const [tips, setTips] = useState<TipWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const {isDarkMode, isNeonMode} = useTheme();
  const insets = useSafeAreaInsets();
  const {navigateTo} = useNavigation();

  // Fetch all tips from the database when the component mounts
  useEffect(() => {
    setLoading(true);
    getAllTips(fetchedTips => {
      setTips(fetchedTips);
      setLoading(false);

      // Fade in animation when tips are loaded
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }).start();
    });
  }, [fadeAnim]);

  // Get colors based on the active theme
  let backgroundColor, textColor, borderColor, primaryColor;

  if (isNeonMode) {
    backgroundColor = Colors.neon.background;
    textColor = Colors.neon.text;
    borderColor = Colors.neon.border;
    primaryColor = Colors.neon.primary;
  } else {
    backgroundColor = isDarkMode
      ? Colors.dark.background
      : Colors.light.background;
    textColor = isDarkMode ? Colors.dark.text : Colors.light.text;
    borderColor = isDarkMode ? Colors.dark.border : Colors.light.border;
    primaryColor = isDarkMode ? Colors.dark.primary : Colors.light.primary;
  }

  // Memoize the tip press handler
  const handleTipPress = React.useCallback(
    (tipId: number) => {
      // Navigate to the TIP_DETAILS screen with the selected tip ID
      navigateTo(ScreenNames.TIP_DETAILS, tipId);
    },
    [navigateTo],
  );

  // Create a memoized item renderer to avoid performance issues
  const renderTipItem = React.useCallback(
    ({item, index}: {item: TipWithDetails; index: number}) => {
      return (
        <AnimatedTipListItem
          item={item}
          index={index}
          fadeAnim={fadeAnim}
          onPress={handleTipPress}
        />
      );
    },
    [fadeAnim, handleTipPress],
  );

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor}]}
      edges={['left', 'right', 'top']}>
      {isNeonMode && (
        <LinearGradient
          colors={['rgba(0,0,0,0)', 'rgba(0,204,255,0.2)']} // Using rgba format for Colors.neon.glow.blue with 0.2 opacity
          style={StyleSheet.absoluteFillObject}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
        />
      )}
      <TipListHeader
        onBackPress={() => navigateTo(ScreenNames.MAIN)}
        borderColor={borderColor}
      />
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={isNeonMode ? Colors.neon.glow.cyan : primaryColor}
          />
          {isNeonMode && (
            <Text
              style={[
                styles.loadingText,
                {
                  color: Colors.neon.glow.magenta,
                  textShadowColor: Colors.neon.glow.magenta,
                  textShadowOffset: {width: 0, height: 0},
                  textShadowRadius: 5,
                },
              ]}>
              Loading Tips...
            </Text>
          )}
        </View>
      ) : (
        <FlatList
          data={tips}
          renderItem={renderTipItem}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={[
            styles.listContent,
            {paddingBottom: Math.max(20, insets.bottom)},
          ]}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text
                style={[
                  styles.emptyText,
                  {
                    color: isNeonMode ? Colors.neon.glow.yellow : textColor,
                    ...(isNeonMode && {
                      textShadowColor: Colors.neon.glow.yellow,
                      textShadowOffset: {width: 0, height: 0},
                      textShadowRadius: 3,
                    }),
                  },
                ]}>
                No tips available. Please check your database.
              </Text>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },

  listContent: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    letterSpacing: 0.5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    textAlign: 'center',
    lineHeight: 24,
    fontWeight: '500',
  },
});

export default AllTipsScreen;
