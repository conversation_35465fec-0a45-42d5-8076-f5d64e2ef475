import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {TipWithDetails} from '../../models/types';
import {useTheme} from '../../theme/ThemeContext';
import Colors from '../../theme/colors';
import LinearGradient from 'react-native-linear-gradient';

// Props for the TipListItem component
interface TipListItemProps {
  item: TipWithDetails;
  onPress?: (tipId: number) => void;
}

// TipCard component type definition
type TipCardProps = {
  tip: TipWithDetails;
  textColor: string;
  backgroundColor: string;
  borderColor: string;
  isNeonMode: boolean;
  isDarkMode: boolean;
  onPress?: (tipId: number) => void;
};

// Get shadow styles based on theme
const getShadowStyle = (isNeonMode: boolean, isDarkMode: boolean) => {
  if (isNeonMode) {
    return {
      shadowColor: Colors.neon.glow.blue,
      shadowOffset: {width: 0, height: 0},
      shadowOpacity: 0.8,
      shadowRadius: 10,
      elevation: 10,
    };
  } else if (isDarkMode) {
    return {
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 5,
    };
  } else {
    return {
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: 0.2,
      shadowRadius: 3,
      elevation: 3,
    };
  }
};

// Image placeholder component
const ImagePlaceholder = ({
  isNeonMode,
  isDarkMode,
}: {
  isNeonMode: boolean;
  isDarkMode: boolean;
}) => {
  if (isNeonMode) {
    return (
      <LinearGradient
        colors={[Colors.neon.glow.magenta, Colors.neon.glow.blue]}
        style={styles.tipImagePlaceholder}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}
      />
    );
  } else {
    return (
      <View
        style={[
          styles.tipImagePlaceholder,
          {
            backgroundColor: isDarkMode
              ? Colors.dark.accent
              : Colors.light.accent,
          },
        ]}
      />
    );
  }
};

// TipCard component for displaying individual tips in the list
const TipCard = ({
  tip,
  textColor,
  backgroundColor,
  borderColor,
  isNeonMode,
  isDarkMode,
  onPress,
}: TipCardProps) => {
  const handlePress = () => {
    if (onPress) {
      onPress(tip.id);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.tipCard,
        {
          backgroundColor,
          borderColor: isNeonMode ? Colors.neon.glow.blue : borderColor,
          borderWidth: isNeonMode ? 1.5 : 1,
        },
        getShadowStyle(isNeonMode, isDarkMode),
      ]}
      onPress={handlePress}
      activeOpacity={0.6}>
      {isNeonMode && (
        <LinearGradient
          colors={['#151528', '#1A1A36']} // Using explicit hex values for neon theme
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
          style={StyleSheet.absoluteFillObject}
        />
      )}
      <View style={styles.tipCardContent}>
        <Text
          style={[
            styles.tipTitle,
            {
              color: isNeonMode ? Colors.neon.glow.cyan : textColor,
              ...(isNeonMode && {
                textShadowColor: Colors.neon.glow.blue,
                textShadowOffset: {width: 0, height: 0},
                textShadowRadius: 5,
              }),
            },
          ]}
          numberOfLines={2}>
          {tip.title}
        </Text>
        <Text
          style={[
            styles.tipSubtitle,
            {
              color: textColor,
              ...(isNeonMode && {
                textShadowColor: Colors.neon.glow.blue,
                textShadowOffset: {width: 0, height: 0},
                textShadowRadius: 2,
              }),
            },
          ]}
          numberOfLines={3}>
          {tip.subtitle}
        </Text>
      </View>
      <View style={styles.tipImageContainer}>
        <ImagePlaceholder isNeonMode={isNeonMode} isDarkMode={isDarkMode} />
      </View>
    </TouchableOpacity>
  );
};

const TipListItem: React.FC<TipListItemProps> = ({item, onPress}) => {
  const {isDarkMode, isNeonMode} = useTheme();

  // Get colors based on the active theme
  let backgroundColor, textColor, borderColor;

  if (isNeonMode) {
    backgroundColor = Colors.neon.surface;
    textColor = Colors.neon.text;
    borderColor = Colors.neon.border;
  } else {
    backgroundColor = isDarkMode ? Colors.dark.surface : Colors.light.surface;
    textColor = isDarkMode ? Colors.dark.text : Colors.light.text;
    borderColor = isDarkMode ? Colors.dark.border : Colors.light.border;
  }

  return (
    <TipCard
      tip={item}
      textColor={textColor}
      backgroundColor={isNeonMode ? Colors.neon.surface : backgroundColor}
      borderColor={borderColor}
      isNeonMode={isNeonMode}
      isDarkMode={isDarkMode}
      onPress={onPress}
    />
  );
};

const styles = StyleSheet.create({
  tipCard: {
    flexDirection: 'row',
    borderRadius: 16,
    marginBottom: 16,
    padding: 16,
    borderWidth: 1,
    overflow: 'hidden',
    position: 'relative',
  },
  tipCardContent: {
    flex: 1,
    marginRight: 12,
  },
  tipTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    letterSpacing: 0.3,
  },
  tipSubtitle: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.9,
  },
  tipImageContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tipImagePlaceholder: {
    width: 64,
    height: 64,
    borderRadius: 32,
  },
});

export default TipListItem;
